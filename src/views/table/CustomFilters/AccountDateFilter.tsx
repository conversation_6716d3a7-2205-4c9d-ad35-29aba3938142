import React, { useEffect, useState, useRef, useMemo } from 'react';

import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import type { SelectChangeEvent } from '@mui/material';

import { useIndexedDB } from '@/hooks/useIndexedDB';
import { tableNames } from '@/data/db';

interface FinancialYearFilterProps {
  filters: Record<string, any>;
  setFilters: (
    filters: Record<string, any> |
    ((prev: Record<string, any>) => Record<string, any>)
  ) => void;
  hideMonthSelect?: boolean;
}

interface FinancialYear {
  account_closing_id: number;
  year_financial: string;
  fy_start_date: string;
  fy_end_date: string;
  closed?: boolean;
}

interface Month {
  label: string;
  value: string;
}

export default function FinancialYearFilterMUI({
  filters,
  setFilters,
  hideMonthSelect = false,
}: FinancialYearFilterProps) {
  const [financialYears, setFinancialYears] = useState<FinancialYear[]>([]);
  const [months, setMonths] = useState<Month[]>([]);

  // IndexedDB hook
  const tables = useMemo(()=>[tableNames.financialYears], []);
  const { getAllValue, isDBConnecting } = useIndexedDB('soc-db', tables);
  const hasLoaded = useRef(false);

  // Load once when DB ready
  useEffect(() => {
    if (isDBConnecting || hasLoaded.current) return;
    hasLoaded.current = true;

    (async () => {
      try {
        const allFys: FinancialYear[] = await getAllValue(
          tableNames.financialYears
        );

        setFinancialYears(allFys || []);
      } catch (err) {
        console.error('Failed to load financial years', err);
      }
    })();
  }, [getAllValue, isDBConnecting]);

  const handleYearChange = (e: SelectChangeEvent) => {
    const yearIdStr = e.target.value;
    
    setFilters(prev => ({
      ...prev,
      financial_year: yearIdStr,
      periodType: '',
      financial_month: '',
      as_on_date: '',
    }));

    const fy = financialYears.find(
      f => f?.year_financial === yearIdStr
    );

    if (fy) {
      const start = new Date(fy.fy_start_date);
      const end = new Date(fy.fy_end_date);
      const gen: Month[] = [];
      const cur = new Date(start);

      while (cur <= end) {
        gen.push({
          label: cur.toLocaleString('default', {
            month: 'long',
            year: 'numeric',
          }),
          value: `${cur.getMonth() + 1}-${cur.getFullYear()}`,
        });
        cur.setMonth(cur.getMonth() + 1);
      }

      setMonths(gen);
    } else {
      setMonths([]);
    }
  };

  const handlePeriodChange = (e: SelectChangeEvent) => {
    const period = e.target.value;

    setFilters(prev => ({
      ...prev,
      periodType: period,
      financial_month: '',
      as_on_date: '',
    }));
  };

  const handleMonthChange = (e: SelectChangeEvent) => {
    console.log(e?.target?.value)
    setFilters(prev => ({
      ...prev,
      financial_month: e.target.value,
    }));
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({
      ...prev,
      as_on_date: e.target.value,
    }));
  };

  return (
    <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
      {/* Financial Year */}
      <FormControl sx={{ minWidth: 180 }} size="small">
        <InputLabel id="fy-label">Financial Year</InputLabel>
        <Select
          labelId="fy-label"
          label="Financial Year"
          value={filters.financial_year ?? ''}
          onChange={handleYearChange}
        >
          {isDBConnecting && (
            <MenuItem disabled>Loading years…</MenuItem>
          )}
          {financialYears.length ? (
            financialYears.map(fy => (
              <MenuItem
                key={fy.year_financial}
                value={String(fy.year_financial)}
              >
                {fy.year_financial}
              </MenuItem>
            ))
          ) : (
            !isDBConnecting && (
              <MenuItem disabled>No years available</MenuItem>
            )
          )}
        </Select>
      </FormControl>

      {/* Period Type */}
      <FormControl sx={{ minWidth: 160 }} size="small" disabled={!filters.financial_year}>
        <InputLabel id="period-label">Period</InputLabel>
        <Select
          labelId="period-label"
          label="Period"
          value={filters.periodType ?? ''}
          onChange={handlePeriodChange}
        >
          <MenuItem value="yearly">Yearly</MenuItem>
          <MenuItem value="monthly">Monthly</MenuItem>
          <MenuItem value="as_on_month">As on Month</MenuItem>
          <MenuItem value="as_on_date">As on Date</MenuItem>
        </Select>
      </FormControl>

      {/* Month */}
      {!hideMonthSelect &&
        (filters.periodType === 'monthly' || filters.periodType === 'as_on_month') && (
          <FormControl sx={{ minWidth: 160 }} size="small">
            <InputLabel id="month-label">Month</InputLabel>
            <Select
              labelId="month-label"
              label="Month"
              value={filters.financial_month ?? ''}
              onChange={handleMonthChange}
            >
              {months.length ? (
                months.map((m, i) => (
                  <MenuItem key={i} value={m.value}>
                    {m.label}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled>No months</MenuItem>
              )}
            </Select>
          </FormControl>
        )}

      {/* Date-Time for As on Date */}
      {filters.periodType === 'as_on_date' && (
        <TextField
          label="As on Date"
          type="date"
          size="small"
          sx={{ minWidth: 220 }}
          slotProps={{
           inputLabel: { shrink: true }
          }}
          value={filters.as_on_date ?? ''}
          onChange={handleDateChange}
        />
      )}
    </Box>
  );
}
