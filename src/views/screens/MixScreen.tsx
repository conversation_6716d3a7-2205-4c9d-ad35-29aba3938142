import Grid from '@mui/material/Grid'
import Box from '@mui/material/Box'

import CommonPageStructure from '@/components/commonPageStructure'
import ComponentRenderer from '@/components/commonPageStructure/ComponentRenderer'
import type { ComponentConfig } from '@/types/menuTypes'

interface MixScreenProps {
  params: Record<string, any>
  pathname: string
  order: ComponentConfig[]
  commonPageConfig?: { title: string; buttons: { label: string; route: string }[] }
  href: string
}

const MixScreen = ({ params, pathname, order, commonPageConfig, href }: MixScreenProps) => {
  // Check if order contains both timeline and card
  const hasTimeline = order.some(component => component.type === 'timeline')
  const cardComponents = order.filter(component => component.type === 'card')
  const hasCard = cardComponents.length > 0

  // Special case: Check if order has exactly 3 components (card, timeline, card)
  const isCardTimelineCardPattern = order.length === 3 &&
    cardComponents.length === 2 &&
    hasTimeline &&
    order[0].type === 'card' &&
    order[1].type === 'timeline' &&
    order[2].type === 'card'

  const shouldMoveCardToRight = hasTimeline && hasCard && !isCardTimelineCardPattern

  // Filter components for left group
  const leftGroup = order.filter((component, index) => {
    const leftTypes = ['table', 'form', 'emailTemplatePdf', 'htmlContent', 'vehicleRegistration']

    if (isCardTimelineCardPattern) {
      // Special case: only first card goes to left group
      return component.type === 'card' && index === 0
    } else if (shouldMoveCardToRight) {
      // If timeline and card both exist, exclude card from left group
      return leftTypes.includes(component.type)
    } else {
      // Normal behavior: include card in left group
      return [...leftTypes, 'card'].includes(component.type)
    }
  })

  // Filter components for right group
  const rightGroup = order.filter((component, index) => {
    const rightTypes = ['timeline', 'helpNote']

    if (isCardTimelineCardPattern) {
      // Special case: timeline and second card go to right group
      return component.type === 'timeline' || (component.type === 'card' && index === 2)
    } else if (shouldMoveCardToRight) {
      // If timeline and card both exist, include card in right group
      return [...rightTypes, 'card'].includes(component.type)
    } else {
      // Normal behavior: only timeline and helpNote
      return rightTypes.includes(component.type)
    }
  })

  const onlyCardsInOrder = order.length > 0 && order.every(component => component.type === 'card')

  // Check if there are exactly 2 tables and 1 card
  const tableCount = order.filter(component => component.type === 'table').length
  const cardCount = order.filter(component => component.type === 'card').length
  const twoTablesOneCard = tableCount === 2 && cardCount === 1 && order.length === 3

  return (
    <CommonPageStructure title={commonPageConfig?.title || 'Default Title'} buttons={commonPageConfig?.buttons || []}>
      <Grid container spacing={5}>
        {/* Special: if exactly 2, first card, second table */}
        {order.length === 2 && order[0].type === 'card' && order[1].type === 'table' ? (
          <Grid container spacing={2} sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <ComponentRenderer component={order[0]} params={params} pathname={pathname} href={href} />
            </Grid>
            <Grid size={{ xs: 12, md: 12 }}>
              <ComponentRenderer component={order[1]} params={params} pathname={pathname} href={href} />
            </Grid>
          </Grid>
        ) : onlyCardsInOrder ? (
          <Grid container spacing={2} sx={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
            {order.map((component, index) => {
              // For cards, use mainGrid from component config, otherwise use default
              const gridSize = component.type === 'card' && (component as any).mainGrid
                ? (component as any).mainGrid
                : { xs: 12, md: 8 };

              return (
                <Grid size={gridSize} key={index}>
                  <ComponentRenderer key={index} component={component} params={params} pathname={pathname} href={href} />
                </Grid>
              );
            })}
          </Grid>
        ) : twoTablesOneCard ? (
          
          // If there are exactly 2 tables and 1 card, render all full width (col-12)
          <Grid container spacing={2}>
            {order.map((component, index) => {
              if (component.type === 'table') {
                return (
                  <Grid size={12} key={index}>
                    <Box sx={{ mb: 3 }}>
                      <ComponentRenderer
                        key={index}
                        component={component}
                        params={params}
                        pathname={pathname}
                        href={href}
                      />
                    </Box>
                  </Grid>
                )
              }

              if (component.type === 'card') {
                // For cards, use mainGrid from component config, otherwise use default 8
                const gridSize = (component as any).mainGrid ? (component as any).mainGrid : 8;
                return (
                  <Grid size={gridSize} key={index}>
                    <ComponentRenderer
                      key={index}
                      component={component}
                      params={params}
                      pathname={pathname}
                      href={href}
                    />
                  </Grid>
                )
              }

              // fallback for other types (shouldn't happen in this case)

              return null
            })}
          </Grid>
        ) : (
          <>
            {/* Left Side: occupies 8/12 columns on medium screens and full width on smaller screens */}
            <Grid size={{ xs: 12, md: 8 }}>
              {leftGroup.map((component, index) => (
                <ComponentRenderer key={index} component={component} params={params} pathname={pathname} href={href} />
              ))}
            </Grid>

            {/* Right Side: occupies 4/12 columns on medium screens and full width on smaller screens */}
            <Grid size={{ xs: 12, md: 4 }}>
              {rightGroup.map((component, index) => (
                <ComponentRenderer key={index} component={component} params={params} pathname={pathname} href={href} />
              ))}
            </Grid>
          </>
        )}
      </Grid>
    </CommonPageStructure>
  )
}

export default MixScreen
