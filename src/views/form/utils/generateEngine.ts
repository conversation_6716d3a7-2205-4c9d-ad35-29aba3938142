import axios from 'axios';
import { Engine } from 'json-rules-engine';
import type {
  Fact
} from 'json-rules-engine';

import { replacePlaceholders } from '@/utils/replacePlaceholders';

// Instantiate a QueryClient to be used for all dynamic fact fetches.
import { queryClient } from "@/contexts/queryClientProvider"
import { getNestedValue, updateNestedValue } from '@/utils/object';
import { evaluateExpression } from '@/utils/evaluate';
import { isAfter, isBefore } from '@/utils/date';
import normalizeRules from './normalizeRules';

import {
  changed,
  notChanged,
  truthy,
  falsy,
  isNumber,
  isArray,
  exists,
  inRange,
  filterBy,
  isInArray,
  isNotInArray,
  greaterThanInArray,
  isArrayEmpty,
  isArrayNotEmpty,
} from './operators';

const isDynamic = (value) => !!value?.dynamic || !!value?.method;

const addParams = (data, defaultParams) => {
  if (!defaultParams || !Array.isArray(defaultParams)) return data;

  // Clone the data to ensure immutability
  const updatedData = { ...data };

  defaultParams.forEach((param) => {
    const path = Array.isArray(param.path) ? param.path : [param.path];

    if (!path) return; // Skip if path is not provided

    const params = param.params;
    const value = getNestedValue(updatedData, path);

    if (!value) return; // Skip if value is not found

    if (Array.isArray(value)) {
      // If the value is an array, update each object in the array
      const updatedArray = value.map((item) => ({
        ...item,
        ...params,
      }));


      // Assign the updated array back to the correct path
      updateNestedValue(updatedData, path, updatedArray);
    } else {
      // If the value is not an array, update it directly
      const updatedValue = { ...value, ...params };

      updateNestedValue(updatedData, path, updatedValue);
    }
  });

  return updatedData;
}

// Custom Fact Type
type CustomFact = {
  fact: string;
  dependsOn?: string;
  dynamic?: boolean;
  method?: string;
  path: string;
};

type GetSchema = (
  facts: Array<Fact & CustomFact & any>,
  rules: Array<any>
) => Engine;

// Generate Engine
const generateEngine: GetSchema = (facts = [], rules = []) => {
  const engine = new Engine([], {
    allowUndefinedFacts: true,
    replaceFactsInEventParams: true
  });

  try {
    engine.addOperator('changed', changed);
    engine.addOperator('notChanged', notChanged);
    engine.addOperator('truthy', truthy);
    engine.addOperator('falsy', falsy);
    engine.addOperator('isNumber', isNumber);
    engine.addOperator('isArray', isArray);
    engine.addOperator('exists', exists);
    engine.addOperator('inRange', inRange);
    engine.addOperator('filterBy', filterBy);
    engine.addOperator('isBefore', isBefore);
    engine.addOperator('isAfter', isAfter);
    engine.addOperator('isInArray', isInArray);
    engine.addOperator('isNotInArray', isNotInArray);
    engine.addOperator('greaterThanInArray', greaterThanInArray);
    engine.addOperator('isArrayEmpty', isArrayEmpty);
    engine.addOperator('isArrayNotEmpty', isArrayNotEmpty);

    // Process Facts
    try {
      facts.forEach(async (fact) => {
        if (!fact?.fact) return;

        if (fact?.onMount) {
          const data = await queryClient.fetchQuery({
            queryKey: [fact?.fact], // Query key based on fact and its dependency value
            queryFn: async () => {
              const res = await axios({
                method: fact?.method || 'GET',
                url: fact?.path,

                // url: replacePlaceholders(fact?.path, { id: value, ...formData }),
                baseURL: process.env.NEXT_PUBLIC_API_URL,
                withCredentials: true,
                validateStatus: () => true,
              });

              let data = null;

              if (fact?.returns === "all") {
                data = res?.data;
              } else {
                data = fact?.returns
                  ? getNestedValue(res?.data?.data, fact.returns)
                  : res?.data?.data;
              }

              if (fact?.defaultParams) {
                data = addParams(data, fact.defaultParams);
              }

              // Retain the side-effect of adding loading fields
              // almanac.addFact("__loadingFields", ["is_billable"]);

              return data || {};
            },
            staleTime: 2 * 60 * 1000
          }
          );

          engine.addFact(fact.fact, data);
        } else if (!isDynamic(fact)) {
          if (fact?.type === 'rowCalculation') {
            engine.addFact(fact.fact, async (params, almanac) => {
              const arr: any[] = await almanac.factValue(fact.dependsOn, {}, fact.xpath);

              if (!arr) return [];

              return arr.map((item) => {
                const result = evaluateExpression(fact.expression, { ...params, ...item });

                return { ...item, [fact.fact]: result };
              });
            });
          } else {
            engine.addFact(fact.fact, fact.value ?? null);
          }
        } else {
          // Using TanStack Query to fetch dynamic facts
          engine.addFact(
            fact.fact,
            async (_, almanac) => {
              const params = fact.params ?? {};
              const allowOn = fact.allowOn ?? {};
              const value = await almanac.factValue(fact?.dependsOn, {}, fact?.xpath);

              // Map each key to a promise that resolves with an object containing the key and the fetched value.
              const paramsPromises = Object.keys(params || {}).map(async key => {
                const paramsKey = params?.[key]?.fact || params?.[key];
                const paramsPath = params?.[key]?.path || params?.[key]?.xpath;

                if (!paramsKey || typeof paramsKey !== "string") {
                  return { key, value: null };
                }

                const value = await almanac.factValue(
                  paramsKey,
                  {},
                  paramsPath
                );

                return { key, value };
              })

              const allowedOnPromises = Object.keys(allowOn || {})?.filter((item) => !(Object.hasOwn(params || {}, item) || item === fact?.dependsOn))?.map(async (key: string) => {
                const factValue = allowOn?.[key];

                const value = await almanac.factValue(
                  factValue?.fact || key,
                  {},
                  factValue?.path || factValue?.xpath
                );

                return { key, value };
              })

              // Wait for all promises to resolve
              const keyValuePairs = await Promise.all([...paramsPromises, ...allowedOnPromises]);

              // Build the formData object from the array of key/value pairs
              const formData = keyValuePairs.reduce((acc, { key, value }) => {
                acc[key] = value;

                return acc;
              }, {});

              if (!value && fact?.dependsOn) {
                return { [fact?.dependsOn]: value };
              }

              // Check if the fact has a condition to allow on certain values only
              if (allowOn) {
                const isNotAllowed = Object?.keys(allowOn)?.some((key) => {
                  const factValue = allowOn?.[key];
                  const formDataValue = formData?.[key]

                  if (Array.isArray(factValue)) {
                    // Fail if the current formData value is NOT included in factValue array
                    return !factValue?.includes(formDataValue);
                  } else if (typeof factValue === "object") {

                    if (!factValue?.operator || typeof factValue?.operator !== "string") return false

                    switch (factValue?.operator) {
                      case "truthy":
                        return !formDataValue
                      case "falsy":
                        return !!formDataValue
                      case "isNumber":
                        return typeof formDataValue !== "number" || Number.isNaN(formDataValue);
                      default:
                        return false
                    }
                  } else if (typeof factValue === "string") {
                    // Fail if the current formData value does not match the string exactly
                    return factValue !== formData?.[key];
                  }

                  // If factValue doesn't match any expected type, consider it a failure
                  return true;
                })

                if (isNotAllowed) return {};
              }

              try {
                const keys = [fact?.fact, value, formData];
                const fromCache = queryClient.getQueryData(keys);

                if (fromCache) return fromCache;

                const data = await queryClient.fetchQuery({
                  queryKey: [fact?.fact, value, formData], // Query key based on fact and its dependency value
                  queryFn: async () => {
                    const url = replacePlaceholders(fact?.path, { id: value, ...formData });

                    const res = await axios({
                      method: fact?.method || 'GET',
                      baseURL: fact?.baseURL || process.env.NEXT_PUBLIC_API_URL,
                      url,
                      withCredentials: true,
                      validateStatus: () => true,
                    });

                    if (!res?.status || res?.status > 399 || !res?.data) {
                      return {};
                    }

                    let data = null;

                    if (fact?.returns === "all") {
                      data = res?.data;
                    } else {
                      data = fact?.returns
                        ? getNestedValue(res?.data?.data, fact.returns)
                        : res?.data?.data;
                    }

                    if (fact?.defaultParams) {
                      data = addParams(data, fact.defaultParams);
                    }

                    // Retain the side-effect of adding loading fields
                    // almanac.addFact("__loadingFields", ["is_billable"]);

                    return data ?? {};
                  },
                  staleTime: 2 * 60 * 1000
                }
                );

                // const hashed = await hash(formData);

                // console.log("Hashed Data", hashed, `${fact.fact}_hash`);
                // almanac.addFact(`${fact.fact}_hash`, hashed);

                // console.log("Fetched Data for", fact.fact, data);

                return data;
              } catch (err: any) {
                console.warn('API Error:', err);

                return {};
              }
            },
            { cache: false }
          );
        }
      });
    } catch (err) {
      console.warn('Error processing facts:', err);
    }

    normalizeRules(rules)?.forEach((rule) => {
      if (!rule?.conditions || !rule?.event) return;

      // Add conditions to the engine
      engine.addRule({
        ...rule,
        priority: rule.priority || 0,
      });
    }
    );

  } catch (err) {
    console.warn('Error generating engine:', err);
  } finally {
    return engine;
  }
};

export default generateEngine;
