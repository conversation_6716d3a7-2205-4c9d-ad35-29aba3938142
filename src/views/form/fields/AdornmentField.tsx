import { FormControl, TextField } from "@mui/material";
import { descriptionId, getTemplate, getUiOptions } from '@rjsf/utils'
import type { FormContextType, RJSFSchema, StrictRJSFSchema } from '@rjsf/utils'

import Adornment from "../components/Adornment";

export type UpdateKeyType =
  | { key: string; value: any; copy?: boolean }
  | { key: string; value: any; copy?: boolean }[];

function AdornmentField<T = any, S extends StrictRJSFSchema = RJSFSchema, F extends FormContextType = any>(props) {
  const { onChange, disabled, label, schema, required, formData, uiSchema, registry, idSchema, rawErrors, formContext, onBlur } = props;

  // console.log(props)

  const description = uiSchema?.['ui:description'] || schema?.description || "";
  const options = getUiOptions<T, S, F>(uiSchema);
  const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options)

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let value: number | string = event.target.value;
    const isValidNumber = !isNaN(parseFloat(value)) && isFinite(value as any);

    if (options?.type === "number" && isValidNumber) {
      value = parseFloat(value) || 0;
    }

    onChange(value);
  };

  const updateKey = (toUpdate: UpdateKeyType) => {
    if (!formContext?.formRef?.current) return;

    const formRef = formContext.formRef.current;
    const state = formRef.state?.formData || {};

    if (Array.isArray(toUpdate)) {
      if (toUpdate.length === 0) return;

      const newState = { ...state };

      toUpdate.forEach(({ key, value, copy = false }) => {
        if (key) newState[key] = copy ? formData : value;
      });

      formRef.onChange(newState);
    } else if (toUpdate && typeof toUpdate === "object" && toUpdate.key) {
      const { key, value, copy = false } = toUpdate;

      formRef.onChange({ ...state, [key]: copy ? formData : value });
    }
  };

  const adornments = {
    ...(options?.startAdornment && Array.isArray(options?.startAdornment) && {
      startAdornment: (
        <>
          {options?.startAdornment?.filter(item => item?.hidden !== true)?.map((adornment, index) => (
            <Adornment
              key={index}
              adornment={adornment}
              position="start"
              updateKey={updateKey}
            />
          ))}
        </>
      ),
    }),
    ...(options?.endAdornment && Array.isArray(options?.endAdornment) && {
      endAdornment: (
        <>
          {options?.endAdornment?.filter(item => item?.hidden !== true)?.map((adornment, index) => (
            <Adornment
              key={index}
              adornment={adornment}
              position="end"
              updateKey={updateKey}
            />
          ))}
        </>
      ),
    }),
  };

  return (
    <FormControl fullWidth={true} required={required}>
      <TextField
        label={label}
        id={`input_${label}`}
        slotProps={{
          input: {
            ...adornments,
            'aria-describedby': descriptionId(idSchema),
          },
        }}
        value={formData?.value || formData}
        onChange={handleChange}
        onBlur={(...args) => {
          onBlur?.(...args);
          
          if (options?.onBlur) {
            updateKey(options.onBlur);
          }
        }}
        disabled={disabled}
        defaultValue={schema?.default ?? ""}
        placeholder={uiSchema?.['ui:placeholder'] || ""}
        required={required}
        error={!!rawErrors?.length}
        helperText={<DescriptionFieldTemplate
          id={descriptionId(idSchema)}
          description={description}
          schema={schema}
          uiSchema={uiSchema}
          registry={registry}
        />}
        type={["string", "number"].includes(String(options?.type) || "string")
          ? (options?.type === "number" ? "number" : "text")
          : "text"}
      />
    </FormControl>
  );
}

;

export default AdornmentField;
