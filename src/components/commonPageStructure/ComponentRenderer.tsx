import { Fragment } from 'react'

import dynamic from 'next/dynamic'

import HtmlRenderer from '@/components/HtmlParse'
import HelpNote from '@/components/card/HelpNoteCard'
import EmailTemplatePdf from '@/components/EmailTemplatePdf'
import TableScreen from '@/views/screens/TableScreen'
import FormBuilder from '@/views/form/builder/server'
import type { ComponentConfig } from '@/types/menuTypes'
import { replacePlaceholders } from '@/utils/replacePlaceholders'

// Dynamically import client-side components
const TimelineComponent = dynamic(() => import('@/views/screens/TimelineScreen'), { ssr: true })

// Import the DynamicCard component
import MemoizedDynamicCard from '@/components/commonPageStructure/MemoizedDynamicCard'

import VehicleRegistrationScreen from '@/views/screens/VehicleRegistrationScreen'

interface ComponentRendererProps {
  component: ComponentConfig
  params: Record<string, any>
  pathname: string
  href: string
  hideTableIfEmpty?: boolean
}

// ✅ **Common function to generate customUrl**
const getCustomUrl = (apiURL: string | undefined, pathname: string, params: Record<string, any>) => {
  const result = replacePlaceholders(apiURL || pathname, params)

  return result
}

const ComponentRenderer = ({ component, params, pathname, href }: ComponentRendererProps) => {
  const customUrl = getCustomUrl((component as any).apiURL, pathname, params) // 🔥 Common logic used here

  switch ((component as any).type) {
    case 'table': {
      return <TableScreen apiURL={customUrl} hideActions={true} hideTableIfEmpty={component?.hideTableIfEmpty}  /> // ✅ Using common customUrl
    }

    case 'card': {
      const cardComponent = component as any

      const {
        headerKeyDisplay,
        keyDisplay,
        renderHtmlBelow,
        htmlContent,
        renderButtonGroup = false
      } = cardComponent

      return (
        <Fragment>
          <MemoizedDynamicCard
            headerKeyDisplay={headerKeyDisplay}
            keyDisplay={keyDisplay || []}
            customUrl={customUrl} // ✅ Using common customUrl
            renderButtonGroup={renderButtonGroup}
            currentPage={1} // Add currentPage as a critical prop
          />
          {renderHtmlBelow && htmlContent && (
            <div style={{ marginTop: '20px', borderTop: '1px solid #ccc', paddingTop: '10px' }}>
              {(() => {
                const htmlApiUrl = getCustomUrl(htmlContent, pathname, params)

                return <HtmlRenderer apiURL={htmlApiUrl} />
              })()}
            </div>
          )}
        </Fragment>
      )
    }

    case 'form': {
      return <FormBuilder href={href} params={params} screenFrom='mix' />
    }

    case 'htmlContent': {
      return <HtmlRenderer apiURL={pathname} />
    }

    case 'timeline': {
      const timelineComponent = component as any
      const { apiURL: timelineApiURL, title, params: componentParams } = timelineComponent

      if (!timelineApiURL) {
        console.error('Timeline component requires apiURL to be specified')

        return <div>Error: Timeline component requires apiURL</div>
      }

      // Use the params from ComponentRenderer (URL extracted params) for URL replacement
      const customTimelineUrl = getCustomUrl(timelineApiURL, pathname, params)

      // Pass both URL extracted params and component params to timeline
      const timelineParams = { ...params, ...componentParams }

      return <TimelineComponent apiURL={customTimelineUrl} title={title} params={timelineParams} />
    }

    case 'helpNote': {
      const helpNoteComponent = component as any
      const { contentType, customContent } = helpNoteComponent

      return <HelpNote contentType={contentType} customContent={customContent} />
    }

    case 'htmlContent': {
      return <HtmlRenderer apiURL={pathname} />
    }

    case 'emailTemplatePdf': {

      return <EmailTemplatePdf apiURL={customUrl} />
    }

    case 'vehicleRegistration': {
      return <VehicleRegistrationScreen />
    }

    default: {
      return <div>Unknown component type</div>
    }
  }
}

export default ComponentRenderer
