'use client'

import React, { useState, useMemo } from 'react'

// MUI Imports
import { useRouter, useSearchParams } from 'next/navigation'

import Box from '@mui/material/Box'
import Paper from '@mui/material/Paper'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import CircularProgress from '@mui/material/CircularProgress'
import { alpha } from '@mui/material/styles'
import InputAdornment from '@mui/material/InputAdornment'
import SearchIcon from '@mui/icons-material/Search'
import Grid from '@mui/material/Grid'
import BusinessIcon from '@mui/icons-material/Business' // Importing a placeholder icon for companies

import axios from 'axios'
import { toast } from 'react-toastify'

// Third Party Components
import classnames from 'classnames'

// Hook Imports
import { useSettings } from '@core/hooks/useSettings'
import { updateCompanyIdCookie, updateCompanyNameCookie, updateToggleModeCookie } from '@/app/server/actions'
import { queryClient } from '@/contexts/queryClientProvider'

// Import match and parse functions from your utilities
import { parse, match } from '@/utils/string'

import { useIndexedDB } from '@/hooks/useIndexedDB'

import { tableNames } from '@/data/db'


// Define your company type
export type CompanyType = {
  company_id: number
  company_name: string
  user_roles: string[]
  toggleMode: 'society' | 'gate'
}

// Grouped companies by first letter
type GroupedCompanies = {
  [letter: string]: CompanyType[]
}

const CompanyDropdown = ({
  companies,
  isPopup = true,
}: {
  companies: CompanyType[]
  isPopup?: boolean
}) => {
  const { settings } = useSettings()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const tables = useMemo(() => [tableNames.financialYears, tableNames.user], []);
  const { isDBConnecting, putKeyValuePairs, deleteAll, putBulkValue } = useIndexedDB("soc-db", tables)

  // Search state and loading state
  const [search, setSearch] = useState('')
  const [loading, setLoading] = useState(false)

  // Group companies by first letter after filtering
  const groupedCompanies: GroupedCompanies = useMemo(() => {
    const filtered = companies.filter((company) =>
      company.company_name.toLowerCase().includes(search.toLowerCase())
    )

    filtered.sort((a, b) => a.company_name.localeCompare(b.company_name))
    const groups: GroupedCompanies = {}

    filtered.forEach((company) => {
      const letter = company.company_name.charAt(0).toUpperCase()

      if (!groups[letter]) groups[letter] = []
      groups[letter].push(company)
    })

    return groups
  }, [companies, search])

  const sortedLetters = useMemo(() => Object.keys(groupedCompanies).sort(), [
    groupedCompanies,
  ])

  const handleCompanySelect = async (company: CompanyType) => {
    if (!company || !company.company_id || isNaN(company.company_id) || loading) {
      return
    }

    setLoading(true)

    try {
      const toggleMode = ["gate", "society"].includes(company?.toggleMode) ? company?.toggleMode : 'society';
      const isIdAdded = await updateCompanyIdCookie(company.company_id)
      const isNameAdded = await updateCompanyNameCookie(company.company_name)
      const isToggleModeAdded = await updateToggleModeCookie(toggleMode)

      if (!isIdAdded || !isNameAdded || !isToggleModeAdded) {
        setLoading(false)
        console.error("Failed to update company cookies.")

        return
      }

      const { data } = await axios.get("/admin/accountsetting/accountset", {
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        validateStatus: () => true,
        withCredentials: true,
      })

      if (data?.status_code === 200 && Array.isArray(data?.data)) {
        await putKeyValuePairs(tableNames.user, {
          company_id: company.company_id,
          company_name: company.company_name,
          toggle_mode: toggleMode,
        })

        const financialYears = data?.data.map((financialYear: any) => {
          const { title, ...rest } = financialYear;

          return rest;
        });

        await deleteAll(tableNames.financialYears)
        await putBulkValue(tableNames.financialYears, financialYears)
      } else {
        toast.warning(
          <div>
            Failed to fetch financial years or data format is incorrect. Filters may not work as expected.
            <br />
            <a href="/faq#financial_years_missing" target="_blank" rel="noopener noreferrer">
              Go to FAQ for more info
            </a>
          </div>
        );
      }

      const redirectTo = searchParams.get('redirectTo')
      const targetPath = `/admin/${toggleMode}/dashboard`
      const isRedirectToValid = redirectTo && redirectTo !== '/admin/select' && redirectTo !== '/admin/dashboard' && redirectTo?.includes(`/${toggleMode}/`)

      replace(isRedirectToValid ? redirectTo : targetPath)

      setTimeout(() => {
        queryClient.invalidateQueries()
        queryClient.refetchQueries()
      }, 1000)

    } catch (error) {
      toast.error('Error selecting company. Please try again.')
      console.error('Error selecting company:', error)
    } finally {
      setLoading(false)
    }
  }

  // Define a fixed width for company cards (matching the image)
  // const CARD_WIDTH = 150; // Approximated width from the image
  const CARD_HEIGHT = 100; // Approximated height from the image

  return (
    <Paper
      className={classnames(
        settings.skin === 'bordered' ? 'border shadow-none' : 'shadow-lg'
      )}
      sx={{
        bgcolor: 'background.paper',
        boxShadow: (theme) => theme.shadows[1],
        borderRadius: 1, // Consistent border radius for the whole component
        p: 2, // Slightly reduced padding for Paper, as per image
        m: isPopup ? 0 : 2,
        display: 'flex',
        flexDirection: 'column',
        height: isPopup ? '100vh' : 'auto',
        maxHeight: isPopup ? '100vh' : '80vh',
        overflow: 'hidden',
        position: 'relative',
        minHeight: 0,
      }}
    >
      {/* Search Bar Section */}
      <Box sx={{ mb: 2 }}> {/* Reduced bottom margin to match image */}
        <TextField
          fullWidth
          size="small" // Smaller to match the image's search bar
          placeholder="Search companies..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          sx={{
            '& .MuiOutlinedInput-notchedOutline': {
              border: 'none',
            },
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'transparent', // Transparent background as in image
              borderRadius: 1,
              '&.Mui-focused': {
                boxShadow: (theme) => `inset 0 0 0 1px ${theme.palette.primary.main}`,
              },
              '&:hover': {
                backgroundColor: (theme) => alpha(theme.palette.action.hover, 0.03),
              },
            },
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {/* Scrollable List Content Area */}
      <Box
        sx={{
          flex: 1,
          minHeight: 0,
          overflowY: 'auto',
          overflowX: 'hidden',
          // No padding here, as the image shows content starting directly
          // from the left edge for letter headers, and cards are in grids.
          // Spacing will be handled by margins/Grid spacing.
          pb: 2, // Keep some padding at the bottom of scroll area for last row

          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: (theme) => alpha(theme.palette.text.secondary, 0.3),
            borderRadius: '10px',
            '&:hover': {
              backgroundColor: (theme) => alpha(theme.palette.text.secondary, 0.5),
            },
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'transparent',
          },
        }}
      >
        {sortedLetters.length > 0 ? (
          sortedLetters.map((letter) => (
            <Box key={letter} sx={{ mb: 3 }}> {/* Increased bottom margin for letter groups */}
              {/* Group Header (Letter) */}
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 2, // Spacing above the first row of cards
                }}
              >
                <Typography
                  variant="h6" // Size looks like h6 in the image
                  color="text.primary" // Standard text color
                  sx={{
                    fontWeight: 600, // Medium bold
                    mr: 2, // Space between letter and line
                    textTransform: 'uppercase', // Ensure uppercase
                    // No explicit padding or border bottom here, line is separate
                  }}
                >
                  {letter}
                </Typography>
                <Box
                  sx={{
                    flexGrow: 1, // Line takes rest of the space
                    height: '1px', // Thin line
                    bgcolor: 'divider', // Subtle grey line
                    ml: 1, // Space after the letter
                  }}
                />
              </Box>

              {/* Company Items: Using Grid for responsive wrapping of fixed-width cards */}
              <Grid
                container
                spacing={2} // Spacing between cards (adjust as needed)
              // Using a 'columns' prop on container allows for more flexible item sizing,
              // but for fixed widths, responsive 'size' prop on item is typically easier.
              // Reverting to Grid v7 'size' prop usage as discussed.
              >
                {groupedCompanies[letter].map((company) => (
                  <Grid
                    // MUI v7 Grid syntax: 'size' prop with responsive breakpoints
                    // xs=12 (1 col on tiny screens), sm=6 (2 cols), md=4 (3 cols), lg=3 (4 cols)
                    // The image suggests more columns on larger screens, let's target 4.
                    size={{ xs: 12, sm: 6, md: 4, lg: 3 }}
                    key={company.company_id}
                    sx={{
                      display: 'flex', // Use flex to center Paper within Grid item
                      justifyContent: 'center', // Center the card
                      // If you want fixed width cards to always maintain their width,
                      // you might need to use a custom grid or adjust Grid columns for fixed CARD_WIDTH.
                      // For now, these size values will make them responsive.
                    }}
                  >
                    <Paper
                      variant="outlined"
                      onClick={() => {
                        if (!loading) {
                          handleCompanySelect(company)
                        }
                      }}
                      sx={{
                        width: "100%", // Fixed width for the card
                        height: CARD_HEIGHT, // Fixed height for the card
                        p: 1.5, // Padding inside the card (adjusted for fixed height)
                        borderRadius: '4px', // Slightly rounded corners as in the image
                        bgcolor: 'background.paper',
                        cursor: loading ? 'not-allowed' : 'pointer',
                        borderColor: (theme) => alpha(theme.palette.text.primary, 0.1), // Subtle border
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center', // Center content horizontally
                        justifyContent: 'center', // Center content vertically
                        textAlign: 'center', // Text alignment for content
                        boxShadow: 'none', // No shadow by default as in image
                        '&:hover': {
                          // Slight border and shadow on hover
                          borderColor: (theme) => theme.palette.primary.light,
                          boxShadow: 1,
                        },
                      }}
                    >
                      <BusinessIcon color="primary" sx={{ fontSize: 32, mb: 0.5 }} /> {/* Icon size and margin adjusted */}
                      <Typography variant="body2" // Smaller text as in image
                        sx={{ fontWeight: 500, color: 'text.primary' }}>
                        {parse(company.company_name, match(company.company_name, search)).map(
                          (segment, i) =>
                            segment.highlight ? (
                              <span key={i} style={{ fontWeight: 700, color: 'primary.main' }}>
                                {segment.text}
                              </span>
                            ) : (
                              <span key={i}>{segment.text}</span>
                            )
                        )}
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', minHeight: '150px' }}>
            <Typography variant="h6" color="text.secondary">
              No companies found.
            </Typography>
          </Box>
        )}
      </Box>

      {/* Loading overlay */}
      {(isDBConnecting || loading) && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            bgcolor: (theme) => alpha(theme.palette.background.paper, 0.8),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 9999,
            borderRadius: 1,
          }}
        >
          <CircularProgress size={50} />
        </Box>
      )}
    </Paper>
  )
}

export default CompanyDropdown
