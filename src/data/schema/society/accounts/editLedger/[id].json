{"meta": {"title": "<PERSON>", "data_source": "accounts/getLedgerById/:id"}, "schema": {"rules": [{"conditions": {"all": [{"fact": "behavior", "operator": "equal", "value": "expense"}]}, "event": [{"type": "uiOverride", "params": {"income_type": {"ui:title": "Expense Type"}}}]}, {"conditions": {"all": [{"fact": "behavior", "operator": "notIn", "value": ["expense", "income"]}]}, "event": [{"type": "remove", "params": {"field": ["income_type"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "accounts/editLedger", "redirect": "/admin/society/accounts/viewLedgers"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/accounts/viewLedgers"}, {"title": "Reset", "type": "reset"}, {"title": "Save & Add New", "type": "submit_and_new", "api_path": "accounts/editLedger"}], "flow": {"children": {"parent_ledger_name": {"title": "Select group", "disabled": true, "required": true, "type": "string"}, "ledger_account_name": {"title": "Ledger Name", "default": "Non occupancy charges", "disabled": true}, "arrAllPreviousFY": {"title": "Opning Balance", "type": "openingBalance", "required": true}, "opning_balance": {"hidden": true, "title": "Office Assignments", "type": "array", "layout": "minimal", "addable": false, "removable": false, "minItems": 1, "required": true, "children": {"label": {"title": "Label", "disabled": true}, "opening_balance": {"title": "Opening Balance", "disabled": true}, "fy": {"title": "Financial Year", "type": "daterange", "disabled": true}}}, "behavior": {"title": "Behaviour", "hidden": true}, "income_type": {"title": "Income Type", "hidden": true}, "amount_debit_credit": {"title": " Amout Credit/Debit", "enum": ["Credit", "Debit"]}, "nature_of_group": {"title": "Nature of Group", "disabled": true}, "parent_id": {"hidden": true, "type": "number"}}}}}