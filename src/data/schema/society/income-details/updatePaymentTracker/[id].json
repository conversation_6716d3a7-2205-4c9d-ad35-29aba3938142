{"meta": {"title": "Receipt Tracker", "data_source": "income-details/updatePaymentTracker/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "income-details/updatePaymentTracker", "redirect": "/admin/society/income-details/incomepaymenttracker"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/income-details/incomepaymenttracker"}, {"title": "Reset", "type": "reset"}], "facts": [{"fact": "bank_balance", "path": "/admin/vendorbill/fetchDataAddVendorPayment/:id", "method": "GET", "dependsOn": "bank_ledger", "xpath": "$.id"}], "rules": [{"conditions": {"all": [{"fact": "show_writeoff", "operator": "truthy"}, {"fact": "writeoff_amount_copy", "operator": "equal", "value": {"fact": "writeoff_amount"}}, {"fact": "receipt_amount_copy", "operator": "equal", "value": {"fact": "receipt_amount"}}]}, "event": [{"type": "calculate", "params": {"receipt_amount": {"fact": "receipt_amount_copy"}, "writeoff_amount": {"fact": "writeoff_amount_copy"}, "__field": "receipt_amount", "__exp": "receipt_amount - writeoff_amount"}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "truthy"}]}, "event": [{"type": "require", "params": {"field": ["tds_amount"]}}, {"type": "schemaOverride", "params": {"tds_amount.maximum": {"fact": "total_due_amount"}}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"tds_amount": 0}}, {"type": "remove", "params": {"field": ["tds_amount"]}}]}, {"conditions": {"all": [{"fact": "show_tds", "operator": "truthy"}, {"fact": "tds_amount", "operator": "greaterThan", "value": {"fact": "total_due_amount"}}]}, "event": [{"type": "uiAppend", "params": {"tds_amount": {"ui:options": {"error": "TDS amount should be less than total due amount."}}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "truthy"}]}, "event": [{"type": "require", "params": {"field": ["writeoff_amount"]}}, {"type": "schemaOverride", "params": {"writeoff_amount.maximum": {"fact": "total_due_amount"}}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "falsy"}]}, "event": [{"type": "update", "params": {"writeoff_amount": 0, "writeoff_amount_copy": 0}}, {"type": "remove", "params": {"field": ["writeoff_amount"]}}]}, {"conditions": {"all": [{"fact": "show_writeoff", "operator": "truthy"}, {"fact": "writeoff_amount", "operator": "greaterThan", "value": {"fact": "total_due_amount"}}]}, "event": [{"type": "uiAppend", "params": {"writeoff_amount": {"ui:options": {"error": "Write off amount should be less than total due amount."}}}}]}, {"conditions": {"all": [{"fact": "bank_balance", "path": "ledger_amount", "operator": "isNumber"}]}, "event": [{"type": "tooltip", "params": {"__title": "[[color:{{isSuccess}}]] Your {{label}} bank balance is currency({{bank_balance}}) [[color]]", "__field": ["bank_ledger"], "bank_balance": {"fact": "bank_balance", "path": "ledger_amount"}, "label": {"fact": "bank_balance", "path": "label"}, "isSuccess": {"fact": "bank_balance", "path": "ledger_amount"}}}]}], "flow": {"children": {"received_from": {"title": "Received From", "type": "string", "required": true}, "transaction_reference": {"title": "Transaction Reference (Cheque number)", "type": "number", "pattern": "[0-9]{6}", "required": true}, "bank_ledger": {"title": "Bank Account", "type": "dropdown", "apiPath": "admin/accounts/viewBankAccounts", "labelKeys": ["ledger_account_name"], "required": true, "placeholder": "Please Select Bank Account", "onMount": true}, "cheque_date": {"title": "Cheque Date", "type": "date"}, "bank_and_branch_name": {"title": "Bank and Branch Name", "type": "string", "required": true}, "late_payment_charges": {"title": "Late Payment Charges", "type": "number", "disabled": true}, "total_due_amount": {"title": "Total Due Amount", "type": "number", "disabled": true}, "receipt_amount": {"title": "Receipt Amount", "type": "number", "required": true, "default": 0, "endAdornment": [{"title": "TDS", "showButton": true, "update": {"key": "show_tds", "value": true}}, {"title": " Write Off", "showButton": true, "update": {"key": "show_writeoff", "value": true}}], "onBlur": {"key": "receipt_amount_copy", "copy": true}}, "receipt_amount_copy": {"type": "number", "disabled": true, "hidden": true}, "tds_amount": {"title": "TDS Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove TDS", "icon": "ri-close-line", "update": {"key": "show_tds", "value": false}}]}, "writeoff_amount": {"title": "Write Off Amount", "type": "number", "description": "", "endAdornment": [{"title": "Remove Write Off", "icon": "ri-close-line", "update": {"key": "show_writeoff", "value": false}}], "onBlur": {"key": "writeoff_amount_copy", "copy": true}}, "writeoff_amount_copy": {"type": "number", "disabled": true, "hidden": true}, "show_tds": {"type": "radio", "hidden": true, "default": false}, "show_writeoff": {"type": "radio", "hidden": true, "default": false}, "receipt_date": {"title": "Receipt Date", "type": "date", "required": true}}}}}