{"meta": {"title": "Edit Transaction", "data_source": "transaction/editTransaction/:id"}, "schema": {"actions": [{"title": "Save", "type": "submit", "api_path": "transaction/editTransaction", "redirect": "/admin/transaction/viewLedgers"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/transaction/viewLedgers"}], "flow": {"title": "Edit Transaction Details", "children": {"transaction_date": {"title": "Transaction Date", "type": "date", "required": true}, "from_ledger_account_name": {"title": "Initial Entry", "type": "group", "children": {"from_ledger_id": {"title": "From/By", "type": "dropdown", "apiPath": "/admin/accounts/viewLedgers?dropdown=journal&data=all", "keys": ["ledger_account_name"], "required": true}, "from_transaction_amount": {"title": "Amount", "type": "number", "required": true}, "from_transaction_type": {"title": "Transaction Type", "type": "select", "enum": [{"const": "cr", "title": "Credit"}, {"const": "dr", "title": "Debit"}], "required": true}, "from_memo_desc": {"title": "Narration", "type": "textarea", "required": true}}}, "to_ledger_account_name": {"title": "Counter Entry", "type": "group", "children": {"to_ledger_id": {"title": "To", "type": "dropdown", "apiPath": "/admin/accounts/viewLedgers?dropdown=journal&data=all", "keys": ["ledger_account_name"], "required": true}, "to_transaction_amount": {"title": "Amount", "type": "number", "required": true}, "to_transaction_type": {"title": "Transaction Type", "type": "select", "enum": [{"const": "cr", "title": "Credit"}, {"const": "dr", "title": "Debit"}], "required": true}, "to_memo_desc": {"title": "Narration", "type": "textarea", "required": true}}}}}}}