{"meta": {"title": "Add Journal Entry Details"}, "schema": {"actions": [{"title": "Save & New", "type": "submit_and_new", "api_path": "transaction/addJournalEntry", "redirect": "/admin/society/transaction/addJournalEntry"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/transaction/addJournalEntry"}], "rules": [{"conditions": {"all": [{"fact": "transaction_amount", "operator": "lessThan", "value": 0}]}, "event": [{"type": "uiSchemaReplace", "params": {"transaction_amount.ui:options.ui:error": "Amount must be a positive number"}}]}, {"conditions": {"all": [{"fact": "transaction_date", "operator": "greaterThan", "value": "{{today}}"}]}, "event": [{"type": "uiSchemaReplace", "params": {"transaction_date.ui:options.ui:error": "Journal date cannot be in the future"}}]}], "flow": {"children": {"transaction_date": {"title": "Journal Date", "type": "date", "maxDateTime": "now", "required": true}, "from_ledger": {"title": "From/Debit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers?dropdown=journal", "keys": ["ledger_account_name"], "required": true}, "to_ledger": {"title": "To/Credit", "type": "dropdown", "apiPath": "admin/accounts/viewLedgers?dropdown=journal", "keys": ["ledger_account_name", "id"], "required": true}, "transaction_amount": {"title": "Amount", "type": "number", "required": true}, "memo_desc": {"title": "Narration", "type": "textarea", "required": true}}}}}