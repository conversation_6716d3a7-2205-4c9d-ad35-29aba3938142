{"meta": {"title": "Contra Details"}, "schema": {"actions": [{"title": "Save & New", "type": "submit_and_new", "api_path": "transaction/addContraEntry", "redirect": "/admin/society/transaction/addContraEntry"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/transaction/addContraEntry"}], "flow": {"children": {"transaction_date": {"title": "Transaction Date", "type": "date", "maxDateTime": "now", "minDateTime": "- 50 year", "required": true}, "from_ledger": {"title": "From/Debit", "type": "dropdown", "apiPath": "/admin/accounts/viewLedgers?dropdown=contra", "keys": ["ledger_account_name"], "required": true}, "to_ledger": {"title": "To/Credit", "type": "dropdown", "apiPath": "/admin/accounts/viewLedgers?dropdown=contra", "keys": ["ledger_account_name", "id"], "required": true}, "transaction_amount": {"title": "Amount", "type": "number", "required": true, "startAdornment": "₹"}, "payment_reference": {"title": "Receipt reference", "type": "string", "required": true}, "memo_desc": {"title": "Narration", "type": "textarea", "required": true}}}}}