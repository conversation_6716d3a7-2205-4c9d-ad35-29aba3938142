import type { VerticalMenuDataType } from '@/types/menuTypes'

const incomeMenu: VerticalMenuDataType[] = [
  {
    label: 'Income',
    icon: 'ri-wallet-3-line',
    children: [
      {
        label: 'Maintainance Dues',
        href: '/admin/society/income-details/incomemember',
        pageType: 'table',
        children: [
          {
            label: 'New Incidental Bill',
            href: '/admin/society/common-billing/addcommonbill'
          },
          {
            label: 'Member Invoices',
            href: '/admin/society/income-details/memberInvoicelist/[id]',
            pageType: 'table'
          },
          {
            label: 'Member Receipts',
            href: '/admin/society/income-details/memberReceiptslist/[id]',
            pageType: 'table'
          },
          {
            label: 'Member Unit Statement',
            href: '/admin/society/income-details/membersUnitStatementReport/type=both&unit=5',
            pageType: 'table'
          },
          {
            label: 'Pending Dues',
            href: '/admin/society/income-details/paymemberbill/[id]'

            // pageType: "mix",
            // commonPageConfig: {
            //   title: 'Pay Member Bills',
            //   buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            // },
            // order: [
            //   {
            //     type: 'table',
            //     apiURL: '/admin/society/income-details/memberInvoicelist/[id]'
            //   },
            //   {
            //     type: 'form'
            //   },
            //   {
            //     type: 'table',
            //     apiURL: '/admin/society/income-details/getReceiptListLatest/[id]'
            //   }
            // ]
          },
          {
            label: 'Pending Dues',
            href: '/admin/society/form/maintenance_action_view_rules/[id]',
            pageType: 'accordion'
          }
        ]
      },
      {
        label: 'Incidental Bills',
        href: '/admin/society/common-billing/listcommonbill/',
        pageType: 'table',
        children: [
          {
            label: 'New Incidental Bill',
            href: '/admin/society/common-billing/addcommonbill'
          },
          {
            label: 'Member Incidental Invoices',
            href: '/admin/society/common-billing/memberInvoicelist/[id]',
            pageType: 'table'
          },
          {
            label: 'Non Member Incidental Invoices',
            href: '/admin/society/common-billing/addcommonbill/[id]'
          },
          {
            label: 'Pay Incidental Bill',
            href: '/admin/society/common-billing/paycommonbill/[id]'
          }
        ]
      },
      {
        label: 'Non Member Bills',
        href: '/admin/society/income-details/incomenonmember',
        pageType: 'table',
        children: [
          {
            label: 'New Non Member Bill',
            href: '/admin/society/income-details/addnonmemberincome'
          },
          {
            label: 'Pay Non Member Bill',
            href: '/admin/society/income-details/pay_nonmember_bill/[id]'
          },
          {
            label: 'Pay Non Member Bill',
            href: '/admin/society/income-details/previewWithGenerate/[id]',
            pageType: 'PDF'
          }
        ]
      },
      {
        label: 'Recipt Tracker',
        href: '/admin/society/income-details/incomepaymenttracker',
        pageType: 'table',
        children: [
          {
            label: 'New Non Member Bill',
            href: '/admin/society/income-details/addnonmemberincome'
          },
          {
            label: 'Edit Incidental Bill',
            href: '/admin/society/income-details/updatePaymentTracker/[id]'
          }
        ]
      },
      {
        label: 'Billabel Items',
        href: '/admin/society/billable-note/list',
        pageType: 'table',
        children: [
          {
            label: 'New Billabel',
            href: '/admin/society/billable-note/add_billable'
          },
          {
            label: 'Edit Credit Note',
            href: '/admin/society/billable-note/add_billable/[id]'
          }
        ]
      },
      {
        label: 'Advances',
        href: '/admin/society/credit-accounts/memberAdvances',
        pageType: 'table',
        children: [
          {
            label: 'View Transactions',
            href: '/admin/society/credit-accounts/viewAllTransactions/member/[id]',
            pageType: 'table'
          },
          {
            label: 'New Advance',
            href: '/admin/society/credit-accounts/add'
          },
          {
            label: 'New Advance',
            href: '/admin/society/credit-accounts/nonMemberAdvances',
            pageType: 'table'
          },
          {
            label: 'View Activity Logs',
            href: '/admin/society/credit-accounts/viewActivityLogs/member/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'View Activity Logs',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 8, lg: 10 },
                href: '/admin/society/credit-accounts/nonMemberAdvances',
                headerKeyDisplay: [
                  { key: 'account_name', label: 'Name :- ', isLink: false, isCheckbox: false },
                  { key: 'status', showChip: false },
                  { key: '()', label: '', showSubHeader: true }
                ],
                keyDisplay: [
                  { key: 'amount', label: 'Amount', keyGrid: 3, valueGrid: 3 },
                  { key: 'payment_mode', label: 'Payment Mode', keyGrid: 3, valueGrid: 3 },
                  { key: 'transaction_type', label: 'CD/DR', keyGrid: 3, valueGrid: 3 },
                  { key: 'narration', label: 'Narration', keyGrid: 3, valueGrid: 3 },
                  { key: 'use_credit', label: 'Type', keyGrid: 3, valueGrid: 3 },
                  { key: 'payment_date', label: 'Payment Date', keyGrid: 3, valueGrid: 3 },
                  { key: 'bill_rectification', label: 'Bill Rectification', keyGrid: 3, valueGrid: 3 },
                  { key: 'adjust_against', label: 'Adjust Against', keyGrid: 3, valueGrid: 3 },
                  { key: 'payment_reference', label: 'Payment Reference', keyGrid: 3, valueGrid: 3 },
                  { key: 'adjust_after', label: 'Adjust After', keyGrid: 3, valueGrid: 3 }
                ]
              },
              {
                type: 'timeline',
                apiURL: '/admin/credit-accounts/viewActivityLogs/nonmembers/activity_log',
                title: 'Credit Account Activity',
                params: { module: 'chsone_credit_accounts' }
              }
            ]
          },
          {
            label: 'View Activity Logs',
            href: '/admin/society/credit-accounts/viewAllTransactions/nonmember/[id]',
            pageType: 'table'
          },
          {
            label: 'View Activity Logs',
            href: '/admin/society/credit-accounts/viewActivityLogs/nonmember/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'View Activity Logs',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 6, lg: 12 },
                href: '/admin/society//credit-accounts/nonMemberAdvances',
                headerKeyDisplay: [
                  { key: 'account_name', label: 'Name :- ', isLink: false, isCheckbox: false },
                  { key: 'status', showChip: false },
                  { key: '()', label: '', showSubHeader: true }
                ],
                keyDisplay: [
                  { key: 'amount', label: 'Amount', keyGrid: 2, valueGrid: 4 },
                  { key: 'payment_mode', label: 'Payment Mode', keyGrid: 2, valueGrid: 4 },
                  { key: 'transaction_type', label: 'CD/DR', keyGrid: 2, valueGrid: 4 },
                  { key: 'narration', label: 'Narration', keyGrid: 2, valueGrid: 4 },
                  { key: 'use_credit', label: 'Type', keyGrid: 2, valueGrid: 4 },
                  { key: 'payment_date', label: 'Payment Date', keyGrid: 2, valueGrid: 4 },
                  { key: 'bill_rectification', label: 'Bill Rectification', keyGrid: 2, valueGrid: 4 },
                  { key: 'adjust_against', label: 'Adjust Against', keyGrid: 2, valueGrid: 4 },
                  { key: 'payment_reference', label: 'Payment Reference', keyGrid: 2, valueGrid: 4 },
                  { key: 'adjust_after', label: 'Adjust After', keyGrid: 2, valueGrid: 4 }
                ]
              },
              {
                type: 'card',
                mainGrid: { xs: 12, md: 6, lg: 12 },
                keyDisplay: [
                  { key: 'created_by', label: 'Created by', keyGrid: 2, valueGrid: 4 },
                  { key: 'created_date', label: 'Created Date', keyGrid: 2, valueGrid: 4 },
                  { key: 'updated_by', label: 'Updated by', keyGrid: 2, valueGrid: 4 },
                  { key: 'updated_date', label: 'Updated Date', keyGrid: 2, valueGrid: 4 }
                ]
              },
              {
                type: 'timeline',
                apiURL: '/admin/credit-accounts/viewActivityLogs/nonmembers/activity_log',
                title: 'Credit Account Activity',
                params: { module: 'chsone_credit_accounts' }
              }
            ]
          },
          {
            label: 'View Activity Logs',
            href: '/admin/society/credit-accounts/add/[id]'
          }
        ]
      },
      {
        label: 'Credit Note',
        href: '/admin/society/credit-accounts/creditNote',
        pageType: 'table',
        children: [
          {
            label: 'New Credit Note',
            href: '/admin/society/credit-accounts/creditNoteAdd'
          }
        ]
      }
    ]
  }
]

export const getIncomeMenu = (): VerticalMenuDataType[] => incomeMenu
