import type { VerticalMenuDataType } from '@/types/menuTypes'

const accountMenu: VerticalMenuDataType[] = [
  {
    label: 'Accounts',
    icon: 'ri-calculator-line',
    children: [
      {
        label: 'Accounts',
        href: '/admin/society/accounts/getAllLedgers',
        pageType: 'table',
        children: [
          {
            label: 'New Ledger',
            href: '/admin/society/accounts/createLedger'
          },
          {
            label: 'Edit Ledger',
            href: '/admin/society/accounts/editLedger/[id]'
          },
          {
            label: ' Non occupancy charges',
            href: '/admin/society/transaction/listTransactionMonthly/[id]',
            pageType: 'table'
          },
          {
            label: 'Download Ledger',
            href: '/admin/society/transaction/editTransaction/[id]'
          }
        ]
      },
      {
        label: 'Charts of Accounts',
        href: '/admin/society/accounts/viewLedgers',
        pageType: 'table'
      },
      {
        label: 'Groups',
        href: '/admin/society/accounts/viewGroups',
        pageType: 'table',
        children: [
          {
            label: 'New Group',
            href: '/admin/society/accounts/createGroup'
          },
          {
            label: 'Edit Group',
            href: '/admin/society/accounts/editGroup/[id]'
          }
        ]
      },
      {
        label: 'Create group',
        hideInNavbar: true,
        href: '/admin/society/accounts/createGroup'
      },
      {
        label: 'Bank Reconciliation',
        href: '/admin/society/accounts/bankReconciliation'
      },
      {
        label: 'Bank Reconciliation',
        href: '/admin/society/accounts/bankReconciliationConfirm',
        pageType: 'table',
        hideInNavbar: true
      },
      {
        label: 'Close Accounts ',
        href: '/admin/society/accountsetting/accountset',
        pageType: 'table'
      },
      {
        label: 'Investments ',
        href: '/admin/society/assets/investmentslist',
        pageType: 'table',
        children: [
          {
            label: 'New Investment',
            href: '/admin/society/assets/addInvestment'
          },
          {
            label: 'Edit Investment',
            href: '/admin/society/assets/editInvestment/[id]'
          },
          {
            label: 'View Investment',
            href: '/admin/society/transaction/listTransactionMonthly/[id]',
            pageType: 'table'
          },
          {
            label: 'Download Investment',
            href: '/admin/society/form/addInterest/[id]'
          }
        ]
      },
      {
        label: 'New Investment',
        hideInNavbar: true,
        href: '/admin/society/assets/addInvestment'
      },
      {
        label: 'Create Ledger',
        hideInNavbar: true,
        href: '/admin/society/accounts/createLedger'
      },
      {
        label: 'Current Assets',
        children: [
          {
            label: 'Bank Accounts',
            href: '/admin/society/accounts/viewBankAccounts',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Bank Accounts',
              buttons: [
                {
                  label: 'Create Bank A/C',
                  route: '/admin/society/accounts/addBankAccount',
                  icon: 'ri-add-line',
                  color: 'primary'
                },
                {
                  label: 'Deposit Cash',
                  route: '/admin/society/accounts/cashDeposit',
                  icon: 'ri-funds-line',
                  color: 'primary'
                },
                {
                  label: 'Withdraw Cash',
                  route: '/admin/society/accounts/cashWithdraw',
                  icon: 'ri-funds-line',
                  color: 'primary'
                }
              ]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 12, lg: 12 },
                headerKeyDisplay: [
                  { key: 'bank_name', label: '', isLink: false, isCheckbox: false },
                  {
                    chips: [
                      { key: 'default_bank_for_incidental', statusType: 'incidental', hideWhenInactive: true },
                      { key: 'default_bank_for_nonmember', statusType: 'nonmember', hideWhenInactive: true }
                    ]
                  },
                  { key: 'status', showChip: true, hideWhenInactive: true },
                  { key: 'bank_city', label: 'Branch :', showSubHeader: true, subHeaderInline: true },
                  {
                    showIcon: true,
                    redirectUrl: '/admin/society/accounts/editBankAccount/[account_id]',
                    IconClassName: 'ri:edit-line'
                  }
                ],
                keyDisplay: [
                  { key: 'account_number', label: 'A/C No', keyGrid: 2, valueGrid: 4 },
                  { key: 'balance', label: 'Account Balance', keyGrid: 2, valueGrid: 4 },
                  { key: 'account_name', label: 'A/C Name', keyGrid: 2, valueGrid: 4 },
                  { key: 'bank_ifsc', label: 'IFSC', keyGrid: 2, valueGrid: 4 },
                  { key: 'bank_address', label: 'Address', keyGrid: 2, valueGrid: 4 },
                  { key: 'ledger_account_name', label: 'Ledger Name', keyGrid: 2, valueGrid: 4 }
                ]
              }
            ],
            children: [
              {
                label: 'Create Bank Account',
                href: '/admin/society/accounts/addBankAccount'
              },
              {
                label: 'Edit Bank Account',
                href: '/admin/society/accounts/editBankAccount/[id]'
              },
              {
                label: 'Cash Deposit',
                href: '/admin/society/accounts/cashDeposit'
              },
              {
                label: 'Cash Withdraw',
                href: '/admin/society/accounts/cashWithdraw'
              }
            ]
          },
          {
            label: 'Cash Accounts',
            href: '/admin/society/accounts/viewCashAccounts',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Cash Accounts',
              buttons: [
                {
                  label: 'Print/ Export',
                  route: '/admin/society/accounts/addCashAccount/[id]',
                  icon: 'ri-add-line',
                  color: 'primary'
                }
              ]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 12, lg: 12 },
                headerKeyDisplay: [
                  { key: 'label', label: '', isLink: false, isCheckbox: false },
                  { key: 'status', showChip: true },
                  {
                    showIcon: false,
                    redirectUrl: '/admin/society/accounts/editCashAccount/1',
                    IconClassName: 'ri:edit-line'
                  }
                ],
                keyDisplay: [
                  { key: 'label', label: 'Ledger Name', keyGrid: 6, valueGrid: 6 },
                  {
                    key: 'ledger_amount',
                    label: 'Ledger Balance',
                    keyGrid: 6,
                    valueGrid: 6,
                    isLink: true,
                    redirectUrl: '/admin/society/transaction/listTransactionMonthly/21'
                  }
                ]
              }
            ],
            children: [
              {
                label: 'Edit Cash Account',
                href: '/admin/society/accounts/editCashAccount/[id]'
              }
            ]
          }
        ]
      },
      {
        label: 'Fixed Assets',
        children: [
          {
            label: 'Assets',
            href: '/admin/society/assets/assetsList',
            pageType: 'table',
            children: [
              {
                label: 'New Asset',
                href: '/admin/society/assets/addAsset'
              },
              {
                label: 'Edit Asset',
                href: '/admin/society/assets/editAsset/[id]'
              },
              {
                label: 'View Asset',
                href: '/admin/society/assets/assetDetails/[id]',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'Asset Details',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'card',
                    mainGrid: { xs: 12, md: 10, lg: 10 },
                    headerKeyDisplay: [{ key: 'asset_name', label: 'Asset Details ', isLink: true, isCheckbox: false }],
                    keyDisplay: [
                      { key: 'assets_name', label: 'Asset Name', keyGrid: 2, valueGrid: 4 },
                      { key: 'assets_tag_number', label: 'Asset Tag No', keyGrid: 2, valueGrid: 4 },
                      { key: 'vendor_name', label: 'Vendor Name', keyGrid: 2, valueGrid: 4 },
                      { key: 'assets_categories_name', label: 'Category', keyGrid: 2, valueGrid: 4 },
                      { key: 'assets_location', label: 'Location', keyGrid: 2, valueGrid: 4 },
                      { key: 'assets_purchase_date', label: 'Purchase Date', keyGrid: 2, valueGrid: 4 },
                      { key: 'assets_cost', label: 'Asset Cost', keyGrid: 2, valueGrid: 4 },
                      { key: 'status', label: 'Status', keyGrid: 2, valueGrid: 4 }
                    ]
                  }
                ]
              },
              {
                label: 'Asset Categories',
                href: '/admin/society/assets/settings',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'Staff Categories',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'form',
                    gridProps: { xs: 12, sm: 12, md: 12 }
                  },
                  {
                    type: 'table',
                    apiURL: '/admin/assets/settings'
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        label: 'Edit Asset',
        hideInNavbar: true,
        children: [
          {
            label: 'New Assets',
            href: '/admin/society/assets/addAsset'
          },
          {
            label: 'Edit Asset',
            href: '/admin/society/assets/editAsset/[id]'
          }
        ]
      },
      {
        label: 'Financial Statements',
        children: [
          {
            label: 'Trial Balance',
            href: '/admin/society/accountsreporting/trial_balance',
            pageType: 'table'
          },
          {
            label: 'Cash Flow',
            href: '/admin/society/accountsreporting/cashflow',
            pageType: 'table'
          },
          {
            label: 'Income And Expenditure',
            href: '/admin/society/accountsreporting/profit_and_lossT',
            pageType: 'table'
          },
          {
            label: 'Balance Sheet',
            href: '/admin/society/accountsreporting/balancesheet',
            pageType: 'table'
          },
          {
            label: 'Incorrect Ledger Entries',
            href: '/admin/society/transaction/incorrect_ledger_entries',
            pageType: 'table'
          }
        ]
      },
      {
        label: 'Tax',
        children: [
          {
            label: 'Tax Classes',
            href: '/admin/society/tax/viewTax',
            pageType: 'table'
          },
          {
            label: 'Add Tax',
            href: '/admin/society/tax/addTax',
            hideInNavbar: true
          },
          {
            label: 'Edit Tax',
            href: '/admin/society/tax/editTax/[id]',
            hideInNavbar: true
          },
          {
            label: 'Tax Exemption',
            href: '/admin/society/taxexemption/viewTaxExemption',
            pageType: 'table',
            children: [
              {
                label: 'Add Tax Exemption',
                href: '/admin/society/taxexemption/addtaxexemption'
              }
            ]
          }
        ]
      },
      {
        label: 'Tax Classes',
        hideInNavbar: true,
        children: [
          {
            label: 'Add Tax Class',
            href: '/admin/society/tax/addTaxClass'
          },
          {
            label: 'View Tax Class',
            href: '/admin/society/tax/viewTaxRule/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Tax Class Details',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'card',
                headerKeyDisplay: [{ key: 'tax_class_name', label: 'Class Name :', isLink: false, isCheckbox: false }],
                keyDisplay: [
                  { key: 'tax_class_description', label: 'Description', keyGrid: 6, valueGrid: 6 },
                  { key: 'tax_categories_footer', label: 'Tax Footer (invoice footer)', keyGrid: 6, valueGrid: 6 },
                ]
              },
              {
                type: 'table'
              }
            ]
          },
          {
            label: 'Edit Tax Class',
            href: '/admin/society/taxexemption/addtaxexemption'
          }
        ]
      },
      {
        label: 'Challans',
        children: [
          {
            label: 'TDS Challans',
            href: '/admin/society/tds-challans/list',
            pageType: 'table',
            children: [
              {
                label: 'TDS Challan',
                href: '/admin/society/tds-challans/add'
              },
              {
                label: 'TDS Challan View',
                href: '/admin/society/tds-challans/view/[id]',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'TDS Challan Details',
                  buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
                },
                order: [
                  {
                    type: 'card',
                    mainGrid: { xs: 8, md: 8, lg: 8 },
                    headerKeyDisplay: [
                      { key: '', label: 'TDS Challan Details', isLink: true, isCheckbox: false },
                      { key: 'status', showChip: false },
                      { key: '()', label: '', showSubHeader: true }
                    ],
                    keyDisplay: [
                      { key: 'challan_no', label: 'Challan No', keyGrid: 3, valueGrid: 3 },
                      { key: 'assessment_year', label: 'Assessment Year', keyGrid: 3, valueGrid: 3 },
                      { key: 'tax_applicable', label: 'Tax Applicable', keyGrid: 3, valueGrid: 3 },
                      { key: 'tan_no', label: 'TAN No', keyGrid: 3, valueGrid: 3 },
                      { key: 'type_of_payment', label: 'Type of Payment', keyGrid: 3, valueGrid: 3 },
                      { key: 'bsr_code', label: 'BSR Code', keyGrid: 3, valueGrid: 3 },
                      { key: 'tender_date', label: 'Tender Date', keyGrid: 3, valueGrid: 3 },
                      { key: 'on_account_of', label: 'On Account of', keyGrid: 3, valueGrid: 3 },
                      { key: 'challan_serial_no', label: 'Challan Serial No', keyGrid: 3, valueGrid: 3 },
                      { key: 'payment_mode', label: 'Payment Mode', keyGrid: 3, valueGrid: 3 },
                      { key: 'total_sum', label: 'Total Amount', keyGrid: 3, valueGrid: 3 },
                      { key: 'submitted_bank', label: 'Bank', keyGrid: 3, valueGrid: 3 }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        label: 'Vouchers',
        children: [
          {
            label: 'Voucher Tracker',
            pageType: 'table',
            href: '/admin/society/transaction/listVoucherTracker'
          },
          {
            label: 'Journal Voucher',
            href: '/admin/society/transaction/addJournalEntry',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Add Journal Entry',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form'
              },
              {
                type: 'timeline',
                apiURL: '/admin/transaction/addJournalEntry/activity_log',
                title: 'Escalation Activity',
                params: { module: 'Journal' }
              }
            ]
          },
          {
            label: 'Contra Voucher',
            href: '/admin/society/transaction/addContraEntry',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Add Contra Entry',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form'
              },
              {
                type: 'timeline',
                apiURL: '/admin/transaction/addContraEntry/activity_log',
                title: 'Contra Entry Activity',
                params: { module: 'Contra' }
              }
            ]
          },
          {
            label: 'Payment Voucher            ',
            href: '/admin/society/transaction/addPaymentVoucher',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Add Payment Voucher',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form'
              },
              {
                type: 'timeline',
                apiURL: '/admin/transaction/addPaymentVoucher/activity_log',
                title: 'Payment Voucher Activity',
                params: { module: 'Payment' }
              }
            ]
          },
          {
            label: 'Multiple Payment Voucher',
            href: '/admin/society/transaction/addMultiplePaymentVoucher',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Add Multiple Payment Voucher',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form'
              },
              {
                type: 'timeline',
                apiURL: '/admin/transaction/addMultiplePaymentVoucher/activity_log',
                title: 'Multiple Payment Voucher Activity',
                params: { module: 'Multiple Payment' }
              }
            ]
          },
          {
            label: 'Debit Note ',
            href: '/admin/society/transaction/addDebitEntry',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Add Debit Note',
              buttons: [{ label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' }]
            },
            order: [
              {
                type: 'form'
              },
              {
                type: 'timeline',
                apiURL: '/admin/transaction/addDebitEntry/activity_log',
                title: 'Debit Note Activity',
                params: { module: 'Debit' }
              }
            ]
          }
        ]
      }
    ]
  }
]

export const getAccountMenu = (): VerticalMenuDataType[] => accountMenu
